RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

Data de treinamento: 2025-07-12 12:00:04

MODELO BINÁRIO:
  • Tipo: XGBoost Binário (sem classe 'Sem Ação')
  • Classes: 0=Venda, 1=Compra
  • Função de perda: Binary cross-entropy (logloss)
  • Threshold de probabilidade: 0.55 (sinais só gerados se prob > 0.55)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, MFI, EMV, Amihud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (11 features)
  • Features lagged: 5 lags para cada feature econométrica
  • Total de features: 96
  • Acurácia geral: 0.641

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 15y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Lags features econométricas: 5
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (96):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade
  14. Weekday
  15. Month
  16. Parkinson_Volatility
  17. MFI
  18. EMV
  19. EMV_MA
  20. Amihud
  21. Roll_Spread
  22. Hurst
  23. Vol_per_Volume
  24. CMF
  25. AD_Line
  26. VO
  27. Volume_Lag_1
  28. Volume_Lag_2
  29. Volume_Lag_3
  30. Volume_Lag_4
  31. Volume_Lag_5
  32. Spread_Lag_1
  33. Spread_Lag_2
  34. Spread_Lag_3
  35. Spread_Lag_4
  36. Spread_Lag_5
  37. Volatilidade_Lag_1
  38. Volatilidade_Lag_2
  39. Volatilidade_Lag_3
  40. Volatilidade_Lag_4
  41. Volatilidade_Lag_5
  42. Parkinson_Volatility_Lag_1
  43. Parkinson_Volatility_Lag_2
  44. Parkinson_Volatility_Lag_3
  45. Parkinson_Volatility_Lag_4
  46. Parkinson_Volatility_Lag_5
  47. MFI_Lag_1
  48. MFI_Lag_2
  49. MFI_Lag_3
  50. MFI_Lag_4
  51. MFI_Lag_5
  52. EMV_Lag_1
  53. EMV_Lag_2
  54. EMV_Lag_3
  55. EMV_Lag_4
  56. EMV_Lag_5
  57. EMV_MA_Lag_1
  58. EMV_MA_Lag_2
  59. EMV_MA_Lag_3
  60. EMV_MA_Lag_4
  61. EMV_MA_Lag_5
  62. Amihud_Lag_1
  63. Amihud_Lag_2
  64. Amihud_Lag_3
  65. Amihud_Lag_4
  66. Amihud_Lag_5
  67. Roll_Spread_Lag_1
  68. Roll_Spread_Lag_2
  69. Roll_Spread_Lag_3
  70. Roll_Spread_Lag_4
  71. Roll_Spread_Lag_5
  72. Hurst_Lag_1
  73. Hurst_Lag_2
  74. Hurst_Lag_3
  75. Hurst_Lag_4
  76. Hurst_Lag_5
  77. Vol_per_Volume_Lag_1
  78. Vol_per_Volume_Lag_2
  79. Vol_per_Volume_Lag_3
  80. Vol_per_Volume_Lag_4
  81. Vol_per_Volume_Lag_5
  82. CMF_Lag_1
  83. CMF_Lag_2
  84. CMF_Lag_3
  85. CMF_Lag_4
  86. CMF_Lag_5
  87. AD_Line_Lag_1
  88. AD_Line_Lag_2
  89. AD_Line_Lag_3
  90. AD_Line_Lag_4
  91. AD_Line_Lag_5
  92. VO_Lag_1
  93. VO_Lag_2
  94. VO_Lag_3
  95. VO_Lag_4
  96. VO_Lag_5

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.641
  • Distribuição das Predições:
    - Venda: 3708 (48.3%)
    - Compra: 3968 (51.7%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
