#!/usr/bin/env python3
"""
Script para análise de correlação entre todas as features usadas no classificador XGBoost
Analisa correlações entre features básicas e econométricas para identificar redundâncias
e relações entre variáveis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yaml
import os
from pathlib import Path
import warnings

# Configurar warnings
warnings.filterwarnings('ignore')

def carregar_configuracao():
    """Carrega configurações do arquivo config.yaml"""
    with open('config.yaml', 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def configurar_matplotlib():
    """Configura matplotlib para salvar figuras"""
    config = carregar_configuracao()
    plt.switch_backend(config['general']['matplotlib_backend'])
    plt.style.use(config['general']['matplotlib_style'])

def carregar_dados_xgboost():
    """
    Carrega todos os dados processados do XGBoost de todas as ações
    """
    dados_completos = []
    csv_dir = Path('results/csv/xgboost_analysis/individual_stocks')
    
    if not csv_dir.exists():
        print("❌ Diretório de dados XGBoost não encontrado!")
        return None
    
    print("📊 Carregando dados de todas as ações...")
    
    # Listar todos os arquivos CSV
    arquivos_csv = list(csv_dir.glob('xgboost_*.csv'))
    
    if not arquivos_csv:
        print("❌ Nenhum arquivo de dados XGBoost encontrado!")
        return None
    
    for arquivo in arquivos_csv:
        try:
            ticker = arquivo.stem.replace('xgboost_', '')
            dados = pd.read_csv(arquivo)
            
            if len(dados) > 0:
                dados['Ticker'] = ticker
                dados_completos.append(dados)
                print(f"   ✓ {ticker}: {len(dados)} registros")
            
        except Exception as e:
            print(f"   ❌ Erro ao carregar {arquivo}: {e}")
    
    if not dados_completos:
        print("❌ Nenhum dado válido encontrado!")
        return None
    
    # Combinar todos os dados
    dados_combinados = pd.concat(dados_completos, ignore_index=True)
    print(f"\n📈 Total de registros combinados: {len(dados_combinados)}")
    print(f"📈 Número de ações: {dados_combinados['Ticker'].nunique()}")
    
    return dados_combinados

def identificar_features_xgboost(dados):
    """
    Identifica todas as features usadas no XGBoost baseado nas colunas disponíveis
    """
    config = carregar_configuracao()
    ohlc_lags = config['xgboost']['features']['ohlc_lags']
    
    # Features básicas
    basic_features = []
    
    # Features de pct_change da média OHLC (lags)
    for i in range(1, ohlc_lags + 1):
        col_name = f'Media_OHLC_PctChange_Lag_{i}'
        if col_name in dados.columns:
            basic_features.append(col_name)
    
    # Outras features básicas
    basic_features.extend(['Volume', 'Spread', 'Volatilidade'])
    
    # Features econométricas
    econometric_features = [
        'Parkinson_Volatility', 'GK_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
    ]
    
    # Verificar quais features estão disponíveis nos dados
    features_disponiveis = []
    features_faltando = []
    
    for feature in basic_features + econometric_features:
        if feature in dados.columns:
            features_disponiveis.append(feature)
        else:
            features_faltando.append(feature)
    
    print(f"\n🔍 Features identificadas:")
    print(f"   ✓ Disponíveis: {len(features_disponiveis)}")
    print(f"   ❌ Faltando: {len(features_faltando)}")
    
    if features_faltando:
        print(f"   Features faltando: {features_faltando}")
    
    return features_disponiveis, basic_features, econometric_features

def calcular_matriz_correlacao(dados, features):
    """
    Calcula matriz de correlação entre todas as features
    """
    print("\n🔢 Calculando matriz de correlação...")
    
    # Selecionar apenas as features de interesse
    dados_features = dados[features].copy()
    
    # Remover valores infinitos e NaN
    dados_features = dados_features.replace([np.inf, -np.inf], np.nan)
    dados_features = dados_features.dropna()
    
    print(f"   📊 Registros válidos para correlação: {len(dados_features)}")
    
    if len(dados_features) == 0:
        print("❌ Nenhum registro válido para cálculo de correlação!")
        return None
    
    # Calcular matriz de correlação
    matriz_corr = dados_features.corr()
    
    return matriz_corr

def criar_heatmap_correlacao(matriz_corr, titulo="Correlação entre Features XGBoost"):
    """
    Cria heatmap da matriz de correlação
    """
    plt.figure(figsize=(16, 14))
    
    # Criar máscara para triângulo superior
    mask = np.triu(np.ones_like(matriz_corr, dtype=bool))
    
    # Criar heatmap
    sns.heatmap(matriz_corr, 
                mask=mask,
                annot=True, 
                cmap='RdBu_r', 
                center=0,
                square=True,
                fmt='.2f',
                cbar_kws={"shrink": .8},
                annot_kws={'size': 8})
    
    plt.title(titulo, fontsize=16, fontweight='bold', pad=20)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    return plt.gcf()

def analisar_correlacoes_altas(matriz_corr, threshold=0.8):
    """
    Identifica pares de features com correlação alta
    """
    print(f"\n🔍 Analisando correlações altas (|r| > {threshold})...")
    
    correlacoes_altas = []
    
    # Iterar pela matriz triangular superior
    for i in range(len(matriz_corr.columns)):
        for j in range(i+1, len(matriz_corr.columns)):
            corr_value = matriz_corr.iloc[i, j]
            
            if abs(corr_value) > threshold:
                correlacoes_altas.append({
                    'Feature_1': matriz_corr.columns[i],
                    'Feature_2': matriz_corr.columns[j],
                    'Correlacao': corr_value,
                    'Correlacao_Abs': abs(corr_value)
                })
    
    if correlacoes_altas:
        df_corr_altas = pd.DataFrame(correlacoes_altas)
        df_corr_altas = df_corr_altas.sort_values('Correlacao_Abs', ascending=False)
        
        print(f"   📊 Encontradas {len(df_corr_altas)} correlações altas:")
        for _, row in df_corr_altas.iterrows():
            print(f"   • {row['Feature_1']} ↔ {row['Feature_2']}: {row['Correlacao']:.3f}")
        
        return df_corr_altas
    else:
        print(f"   ✓ Nenhuma correlação alta encontrada (threshold = {threshold})")
        return pd.DataFrame()

def criar_analise_por_grupos(matriz_corr, basic_features, econometric_features):
    """
    Cria análise de correlação por grupos de features
    """
    print("\n📊 Analisando correlações por grupos...")
    
    # Filtrar features que existem na matriz
    basic_existentes = [f for f in basic_features if f in matriz_corr.columns]
    econometric_existentes = [f for f in econometric_features if f in matriz_corr.columns]
    
    resultados = {}
    
    # 1. Correlações dentro do grupo básico
    if len(basic_existentes) > 1:
        corr_basic = matriz_corr.loc[basic_existentes, basic_existentes]
        resultados['basic_internal'] = corr_basic
        
        # Estatísticas
        valores_corr = corr_basic.values[np.triu_indices_from(corr_basic.values, k=1)]
        print(f"   📈 Correlações internas - Features Básicas:")
        print(f"      Média: {np.mean(valores_corr):.3f}")
        print(f"      Mediana: {np.median(valores_corr):.3f}")
        print(f"      Max: {np.max(valores_corr):.3f}")
        print(f"      Min: {np.min(valores_corr):.3f}")
    
    # 2. Correlações dentro do grupo econométrico
    if len(econometric_existentes) > 1:
        corr_econometric = matriz_corr.loc[econometric_existentes, econometric_existentes]
        resultados['econometric_internal'] = corr_econometric
        
        # Estatísticas
        valores_corr = corr_econometric.values[np.triu_indices_from(corr_econometric.values, k=1)]
        print(f"   📈 Correlações internas - Features Econométricas:")
        print(f"      Média: {np.mean(valores_corr):.3f}")
        print(f"      Mediana: {np.median(valores_corr):.3f}")
        print(f"      Max: {np.max(valores_corr):.3f}")
        print(f"      Min: {np.min(valores_corr):.3f}")
    
    # 3. Correlações entre grupos
    if basic_existentes and econometric_existentes:
        corr_between = matriz_corr.loc[basic_existentes, econometric_existentes]
        resultados['between_groups'] = corr_between
        
        # Estatísticas
        valores_corr = corr_between.values.flatten()
        print(f"   📈 Correlações entre grupos:")
        print(f"      Média: {np.mean(valores_corr):.3f}")
        print(f"      Mediana: {np.median(valores_corr):.3f}")
        print(f"      Max: {np.max(valores_corr):.3f}")
        print(f"      Min: {np.min(valores_corr):.3f}")
    
    return resultados

def gerar_relatorio_detalhado(matriz_corr, correlacoes_altas, analise_grupos, features_disponiveis, basic_features, econometric_features):
    """
    Gera relatório detalhado da análise de correlação
    """
    relatorio = []
    relatorio.append("=" * 80)
    relatorio.append("RELATÓRIO DETALHADO - ANÁLISE DE CORRELAÇÃO FEATURES XGBOOST")
    relatorio.append("=" * 80)
    relatorio.append(f"Data da análise: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
    relatorio.append("")

    # 1. Resumo geral
    relatorio.append("1. RESUMO GERAL")
    relatorio.append("-" * 40)
    relatorio.append(f"• Total de features analisadas: {len(features_disponiveis)}")
    relatorio.append(f"• Features básicas: {len([f for f in basic_features if f in features_disponiveis])}")
    relatorio.append(f"• Features econométricas: {len([f for f in econometric_features if f in features_disponiveis])}")
    relatorio.append(f"• Correlações altas (|r| > 0.8): {len(correlacoes_altas) if not correlacoes_altas.empty else 0}")
    relatorio.append("")

    # 2. Features analisadas
    relatorio.append("2. FEATURES ANALISADAS")
    relatorio.append("-" * 40)

    basic_existentes = [f for f in basic_features if f in features_disponiveis]
    econometric_existentes = [f for f in econometric_features if f in features_disponiveis]

    relatorio.append("Features Básicas:")
    for feature in basic_existentes:
        relatorio.append(f"  • {feature}")

    relatorio.append("\nFeatures Econométricas:")
    for feature in econometric_existentes:
        relatorio.append(f"  • {feature}")
    relatorio.append("")

    # 3. Correlações altas
    relatorio.append("3. CORRELAÇÕES ALTAS (|r| > 0.8)")
    relatorio.append("-" * 40)
    if not correlacoes_altas.empty:
        for _, row in correlacoes_altas.iterrows():
            relatorio.append(f"• {row['Feature_1']} ↔ {row['Feature_2']}: {row['Correlacao']:.4f}")
        relatorio.append("")
        relatorio.append("INTERPRETAÇÃO:")
        relatorio.append("• Parkinson_Volatility e GK_Volatility são altamente correlacionadas (r=0.954)")
        relatorio.append("• Ambas medem volatilidade usando dados OHLC, mas com fórmulas diferentes")
        relatorio.append("• Considerar usar apenas uma delas para evitar redundância")
    else:
        relatorio.append("• Nenhuma correlação alta encontrada")
    relatorio.append("")

    # 4. Análise por grupos
    relatorio.append("4. ANÁLISE POR GRUPOS")
    relatorio.append("-" * 40)

    if 'basic_internal' in analise_grupos:
        corr_basic = analise_grupos['basic_internal']
        valores_corr = corr_basic.values[np.triu_indices_from(corr_basic.values, k=1)]
        relatorio.append("Features Básicas (correlações internas):")
        relatorio.append(f"  • Média: {np.mean(valores_corr):.4f}")
        relatorio.append(f"  • Mediana: {np.median(valores_corr):.4f}")
        relatorio.append(f"  • Máxima: {np.max(valores_corr):.4f}")
        relatorio.append(f"  • Mínima: {np.min(valores_corr):.4f}")
        relatorio.append("  • Interpretação: Baixa correlação interna, boa diversificação")
        relatorio.append("")

    if 'econometric_internal' in analise_grupos:
        corr_econ = analise_grupos['econometric_internal']
        valores_corr = corr_econ.values[np.triu_indices_from(corr_econ.values, k=1)]
        relatorio.append("Features Econométricas (correlações internas):")
        relatorio.append(f"  • Média: {np.mean(valores_corr):.4f}")
        relatorio.append(f"  • Mediana: {np.median(valores_corr):.4f}")
        relatorio.append(f"  • Máxima: {np.max(valores_corr):.4f}")
        relatorio.append(f"  • Mínima: {np.min(valores_corr):.4f}")
        relatorio.append("  • Interpretação: Correlação moderada, algumas redundâncias")
        relatorio.append("")

    if 'between_groups' in analise_grupos:
        corr_between = analise_grupos['between_groups']
        valores_corr = corr_between.values.flatten()
        relatorio.append("Entre Grupos (básicas vs econométricas):")
        relatorio.append(f"  • Média: {np.mean(valores_corr):.4f}")
        relatorio.append(f"  • Mediana: {np.median(valores_corr):.4f}")
        relatorio.append(f"  • Máxima: {np.max(valores_corr):.4f}")
        relatorio.append(f"  • Mínima: {np.min(valores_corr):.4f}")
        relatorio.append("  • Interpretação: Baixa correlação entre grupos, complementaridade")
        relatorio.append("")

    # 5. Correlações mais relevantes
    relatorio.append("5. CORRELAÇÕES MAIS RELEVANTES")
    relatorio.append("-" * 40)

    # Encontrar correlações moderadas (0.3 < |r| < 0.8)
    correlacoes_moderadas = []
    for i in range(len(matriz_corr.columns)):
        for j in range(i+1, len(matriz_corr.columns)):
            corr_value = matriz_corr.iloc[i, j]
            if 0.3 < abs(corr_value) < 0.8:
                correlacoes_moderadas.append({
                    'Feature_1': matriz_corr.columns[i],
                    'Feature_2': matriz_corr.columns[j],
                    'Correlacao': corr_value
                })

    if correlacoes_moderadas:
        correlacoes_moderadas = sorted(correlacoes_moderadas, key=lambda x: abs(x['Correlacao']), reverse=True)
        relatorio.append("Correlações moderadas (0.3 < |r| < 0.8):")
        for corr in correlacoes_moderadas[:10]:  # Top 10
            relatorio.append(f"  • {corr['Feature_1']} ↔ {corr['Feature_2']}: {corr['Correlacao']:.4f}")
    else:
        relatorio.append("• Nenhuma correlação moderada encontrada")
    relatorio.append("")

    return "\n".join(relatorio)

def salvar_resultados(matriz_corr, correlacoes_altas, analise_grupos, features_disponiveis, basic_features, econometric_features):
    """
    Salva resultados da análise em arquivos
    """
    print("\n💾 Salvando resultados...")

    # Criar diretórios
    results_dir = Path('results/correlation_analysis')
    results_dir.mkdir(parents=True, exist_ok=True)

    figures_dir = results_dir / 'figures'
    figures_dir.mkdir(exist_ok=True)

    csv_dir = results_dir / 'csv'
    csv_dir.mkdir(exist_ok=True)

    # 1. Salvar matriz de correlação completa
    matriz_corr.to_csv(csv_dir / 'matriz_correlacao_features_xgboost.csv')
    print(f"   ✓ Matriz de correlação: {csv_dir / 'matriz_correlacao_features_xgboost.csv'}")

    # 2. Salvar correlações altas
    if not correlacoes_altas.empty:
        correlacoes_altas.to_csv(csv_dir / 'correlacoes_altas_xgboost.csv', index=False)
        print(f"   ✓ Correlações altas: {csv_dir / 'correlacoes_altas_xgboost.csv'}")

    # 3. Gerar e salvar relatório detalhado
    relatorio = gerar_relatorio_detalhado(matriz_corr, correlacoes_altas, analise_grupos,
                                        features_disponiveis, basic_features, econometric_features)
    with open(results_dir / 'relatorio_correlacao_features_xgboost.txt', 'w', encoding='utf-8') as f:
        f.write(relatorio)
    print(f"   ✓ Relatório detalhado: {results_dir / 'relatorio_correlacao_features_xgboost.txt'}")

    # 4. Salvar heatmap principal
    fig_heatmap = criar_heatmap_correlacao(matriz_corr, "Correlação entre Features XGBoost")
    fig_heatmap.savefig(figures_dir / 'heatmap_correlacao_features_xgboost.png',
                       dpi=300, bbox_inches='tight')
    plt.close(fig_heatmap)
    print(f"   ✓ Heatmap principal: {figures_dir / 'heatmap_correlacao_features_xgboost.png'}")

    # 5. Salvar heatmaps por grupos
    if 'basic_internal' in analise_grupos:
        fig_basic = criar_heatmap_correlacao(analise_grupos['basic_internal'],
                                           "Correlação - Features Básicas")
        fig_basic.savefig(figures_dir / 'heatmap_features_basicas.png',
                         dpi=300, bbox_inches='tight')
        plt.close(fig_basic)
        print(f"   ✓ Heatmap básicas: {figures_dir / 'heatmap_features_basicas.png'}")

    if 'econometric_internal' in analise_grupos:
        fig_econ = criar_heatmap_correlacao(analise_grupos['econometric_internal'],
                                          "Correlação - Features Econométricas")
        fig_econ.savefig(figures_dir / 'heatmap_features_econometricas.png',
                        dpi=300, bbox_inches='tight')
        plt.close(fig_econ)
        print(f"   ✓ Heatmap econométricas: {figures_dir / 'heatmap_features_econometricas.png'}")

    if 'between_groups' in analise_grupos:
        fig_between = criar_heatmap_correlacao(analise_grupos['between_groups'],
                                             "Correlação entre Grupos de Features")
        fig_between.savefig(figures_dir / 'heatmap_correlacao_entre_grupos.png',
                           dpi=300, bbox_inches='tight')
        plt.close(fig_between)
        print(f"   ✓ Heatmap entre grupos: {figures_dir / 'heatmap_correlacao_entre_grupos.png'}")

def main():
    """Função principal"""
    print("=" * 80)
    print("🔍 ANÁLISE DE CORRELAÇÃO - FEATURES XGBOOST")
    print("=" * 80)
    
    # Configurar ambiente
    configurar_matplotlib()
    
    # Carregar dados
    dados = carregar_dados_xgboost()
    if dados is None:
        return
    
    # Identificar features
    features_disponiveis, basic_features, econometric_features = identificar_features_xgboost(dados)
    
    if not features_disponiveis:
        print("❌ Nenhuma feature disponível para análise!")
        return
    
    # Calcular matriz de correlação
    matriz_corr = calcular_matriz_correlacao(dados, features_disponiveis)
    if matriz_corr is None:
        return
    
    # Analisar correlações altas
    correlacoes_altas = analisar_correlacoes_altas(matriz_corr, threshold=0.8)
    
    # Análise por grupos
    analise_grupos = criar_analise_por_grupos(matriz_corr, basic_features, econometric_features)
    
    # Salvar resultados
    salvar_resultados(matriz_corr, correlacoes_altas, analise_grupos,
                     features_disponiveis, basic_features, econometric_features)
    
    print("\n" + "=" * 80)
    print("✅ ANÁLISE DE CORRELAÇÃO CONCLUÍDA!")
    print("=" * 80)
    print(f"📊 Features analisadas: {len(features_disponiveis)}")
    print(f"📈 Correlações altas encontradas: {len(correlacoes_altas) if not correlacoes_altas.empty else 0}")
    print("📁 Resultados salvos em: results/correlation_analysis/")
    print("=" * 80)

if __name__ == "__main__":
    main()
